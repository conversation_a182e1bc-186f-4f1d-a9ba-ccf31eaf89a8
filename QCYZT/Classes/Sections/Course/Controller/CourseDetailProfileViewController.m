//
//  CourseDetailProfileViewController.m
//  QCYZT
//
//  Created by zeng on 2022/9/26.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "CourseDetailProfileViewController.h"
#import "CourseDetailProfileCell.h"
#import "HttpRequestTool+Course.h"
@interface CourseDetailProfileViewController ()<UITableViewDelegate, UITableViewDataSource,FMCommentCellDelegate>

@property (nonatomic, strong) NSMutableArray *dataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;
@property (nonatomic, assign) NSInteger totalComments;
@property (nonatomic,copy) NSString *courseId;

@end

@implementation CourseDetailProfileViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 10;
    
    [self configTableView];
    self.courseId = self.detailModel.courseId;
    [self requestData:self.courseId];
}

- (void)updateCommentData:(NSString *)courseId {
    self.courseId = courseId;
    [self.dataArr removeAllObjects];
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 10;
    [self requestData:courseId];
}

#pragma mark - UITableView
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 2;
}
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
        return 1;
    }
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        CourseDetailProfileCell *cell = [tableView reuseCellClass:[CourseDetailProfileCell class]];
        cell.model = self.detailModel;
        return cell;
    }

    FMCommentCell *cell = [tableView reuseCellClass:[FMCommentCell class]];
    FMCommentFrameModel *frameModel = [self.dataArr objectAtIndex:indexPath.row];
    cell.allowBigFont = YES;
    cell.frameModel = frameModel;
    cell.delegate = self;
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.section == 0) {
        return [tableView fd_heightForCellWithIdentifier:NSStringFromClass([CourseDetailProfileCell class]) configuration:^(CourseDetailProfileCell *cell) {
            cell.model = self.detailModel;
        }];
    }

    FMCommentFrameModel *frameModel = [self.dataArr objectAtIndex:indexPath.row];
    return frameModel.cellHeight;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return nil;
    }

    UIView *view = [[UIView alloc] init];
    view.backgroundColor = UIColor.up_contentBgColor;
    
    UILabel *label = [[UILabel alloc] init];
    [view addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.centerY.equalTo(@0);
    }];
    label.textColor = UIColor.up_textPrimaryColor;
    label.font = [FMHelper scaleFont:15];
    if (self.totalComments > 0) {
        label.text = [NSString stringWithFormat:@"热门评论(%zd)", self.totalComments];
    } else {
        label.text = @"热门评论";
    }
    
    return view;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return CGFLOAT_MIN;
    }

    return 45.0f;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *view = [UIView new];
    view.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    return view;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == 0) {
        return 10.0f;
    }
    
    return CGFLOAT_MIN;
}

#pragma mark - FMNoteCommentCell Delegate
- (void)commentCellReplyBtnDidClicked:(FMCommentModel *)commentModel {
    if ([FMHelper checkLoginStatus]) {
        if (self.commentCellReplyBlock) {
            self.commentCellReplyBlock(commentModel);
        }
    }
}

- (void)commentCellPraiseBtnDidClicked:(FMCommentModel *)commentModel success:(void(^)())success {
    [HttpRequestTool courseCommentPraiseWithCommentId:commentModel.commentId CourseType:self.courseType start:^{
        [SVProgressHUD show];
    } failure:^{
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [SVProgressHUD dismiss];
            success();
        } else {
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}


#pragma mark - HTTP
- (void)requestData:(NSString *)courseId {
    WEAKSELF;
    [HttpRequestTool getCourseCommentListWithWithPage:self.page pageSize:self.pageSize CourseType:self.courseType courseId:courseId start:^{
    } failure:^{
        [__weakSelf endRefreshForFailure];
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            [__weakSelf endRefreshForSuccess];
            if (__weakSelf.page == 1) {
                [__weakSelf.tableView.mj_footer resetNoMoreData];
                [__weakSelf.dataArr removeAllObjects];
            }
            
            __weakSelf.totalComments = [dic[@"total"] integerValue];
            NSArray *dataArr = [NSArray modelArrayWithClass:[FMCommentModel class] json:dic[@"data"]];
            if (dataArr.count < __weakSelf.pageSize) {
                [__weakSelf.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            
            [__weakSelf dealCommentFramesWithComments:dataArr];
            if (!__weakSelf.dataArr.count) {
                UIView *tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, 200)];
                [tableFooterView showNoDataViewWithString:@"暂无评论, 快来抢沙发" attributes:nil position:ShowPositionTop offsetX:0 offsetY:30];
                __weakSelf.tableView.tableFooterView = tableFooterView;
                __weakSelf.tableView.mj_footer.hidden = YES;
            } else {
                __weakSelf.tableView.tableFooterView = nil;
                __weakSelf.tableView.mj_footer.hidden = NO;
            }

            if (__weakSelf.scrollToComment) {
                __weakSelf.scrollToComment = NO;

                if (__weakSelf.dataArr.count) {
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [__weakSelf.tableView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:1] atScrollPosition:UITableViewScrollPositionTop animated:YES];
                    });
                }
            }

            [__weakSelf.tableView reloadData];
            [SVProgressHUD dismiss];
        } else {
            [__weakSelf endRefreshForFailure];
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - Public
// 添加评论
- (void)addCommentWithContent:(NSString *)content commentId:(NSString *)commentId {
    FMCommentModel *model = [[FMCommentModel alloc] init];
    model.commentContent = content;
    model.praisedNum = 0;
    model.commenterIco = [FMUserDefault getUserFace];
    model.commenterName = [FMUserDefault getNickName];
    model.commentTime = [[NSDate date] timeIntervalSince1970] * 1000;
    model.commentId = commentId;
    model.isPraised = NO;
    model.lastComments = @[];
    
    FMCommentFrameModel *frameModel = [[FMCommentFrameModel alloc] init];
    frameModel.allowBigFont = YES;
    frameModel.commentModel = model;
    [self.dataArr insertObject:frameModel atIndex:0];

    self.totalComments++;
    self.tableView.tableFooterView = nil;
    [self.tableView.mj_footer endRefreshingWithNoMoreData];

    [self.tableView reloadData];
}

// 添加回复
- (void)addReplyWithContent:(NSString *)content commentId:(NSString *)commentId commentCell:(FMCommentCell *)cell {
    FMCommentModel *model = [[FMCommentModel alloc] init];
    model.commentContent = content;
    model.commenterIco = [FMUserDefault getUserFace];
    model.commenterName = [FMUserDefault getNickName];
    model.commentTime = [[NSDate date] timeIntervalSince1970] * 1000;
    model.commentId = commentId;

    FMCommentModel *commentModel = cell.frameModel.commentModel;
    if (!commentModel.lastComments) {
        commentModel.lastComments = [NSArray array];
    }
    NSMutableArray *tmpArr = [commentModel.lastComments mutableCopy];
    [tmpArr insertObject:model atIndex:0];
    commentModel.lastComments = [tmpArr copy];
    cell.frameModel.commentModel = commentModel;

    [self.tableView reloadData];
}

#pragma mark - Private
- (void)configTableView {
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [self.tableView registerCellClass:[CourseDetailProfileCell class]];
    [self.tableView registerCellClass:[FMCommentCell class]];

    WEAKSELF;
    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingBlock:^{
        __weakSelf.page++;
        [__weakSelf requestData:__weakSelf.courseId];
    }];
    footer.stateLabel.textColor = ColorWithHex(0xbfbfbf);
    self.tableView.mj_footer = footer;
    
    self.tableView.backgroundColor = UIColor.up_contentBgColor;
}

- (void)endRefreshForFailure {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];

    self.page = self.currentPage;
}

- (void)endRefreshForSuccess {
    [self.tableView.mj_footer endRefreshing];
    [self.tableView.mj_header endRefreshing];
    
    self.currentPage = self.page;
}

/**
 处理评论model
 */
- (void)dealCommentFramesWithComments:(NSArray *)comments {
    NSMutableArray *tmpFrameArr = [NSMutableArray array];
    for (FMCommentModel *commentModel in comments) {
        FMCommentFrameModel *frameModel = [[FMCommentFrameModel alloc] init];
        frameModel.allowBigFont = YES;
        frameModel.allowBigFont = YES;
        frameModel.commentModel = commentModel;
        [tmpFrameArr addObject:frameModel];
    }
    
    [self removeRepeatDataWithArray:tmpFrameArr];
}

/**
 移除重复数据
 */
- (void)removeRepeatDataWithArray:(NSArray *)array {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    for (FMCommentFrameModel *frameModel in array) {
        [dic setObject:frameModel forKey:frameModel.commentModel.commentId];
    }
    
    for (FMCommentFrameModel *frameModel in self.dataArr.reverseObjectEnumerator) {
        if ([dic valueForKey:frameModel.commentModel.commentId]) {
            [self.dataArr removeObject:frameModel];
        }
    }
    
    [self.dataArr addObjectsFromArray:array];
}

#pragma mark - Setter/Getter
- (NSMutableArray *)dataArr {
    if (_dataArr == nil) {
        _dataArr = [[NSMutableArray alloc] init];
    }
    return _dataArr;
}


@end
